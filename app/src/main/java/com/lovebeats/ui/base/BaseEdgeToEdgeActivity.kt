package com.lovebeats.ui.base

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.lovebeats.utils.EdgeToEdgeUtils
import timber.log.Timber

/**
 * Base activity that provides edge-to-edge display support for Android 15+
 * All activities should extend this class to ensure proper edge-to-edge handling
 */
abstract class BaseEdgeToEdgeActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        // Enable edge-to-edge before calling super.onCreate()
        enableEdgeToEdgeDisplay()
        
        super.onCreate(savedInstanceState)
        
        // Apply insets after content view is set
        // Subclasses should call applyWindowInsets() after setContentView()
    }

    /**
     * Enable edge-to-edge display for this activity
     * This is called automatically in onCreate()
     */
    private fun enableEdgeToEdgeDisplay() {
        try {
            EdgeToEdgeUtils.enableEdgeToEdge(this)
            Timber.d("Edge-to-edge enabled for ${this::class.java.simpleName}")
        } catch (e: Exception) {
            Timber.e(e, "Failed to enable edge-to-edge for ${this::class.java.simpleName}")
        }
    }

    /**
     * Apply window insets to the root view
     * Subclasses should call this method after setContentView()
     * 
     * @param applyTop Whether to apply top insets (status bar)
     * @param applyBottom Whether to apply bottom insets (navigation bar)
     */
    protected fun applyWindowInsets(applyTop: Boolean = true, applyBottom: Boolean = true) {
        try {
            val rootView = findViewById<android.view.View>(android.R.id.content)
            rootView?.let { view ->
                EdgeToEdgeUtils.applyWindowInsets(view, applyTop, applyBottom)
                Timber.d("Window insets applied for ${this::class.java.simpleName}")
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to apply window insets for ${this::class.java.simpleName}")
        }
    }

    /**
     * Apply window insets to a specific view
     * Useful for custom inset handling
     */
    protected fun applyWindowInsetsToView(view: android.view.View, applyTop: Boolean = true, applyBottom: Boolean = true) {
        EdgeToEdgeUtils.applyWindowInsets(view, applyTop, applyBottom)
    }

    /**
     * Apply window insets as margins to a specific view
     * Useful when padding is not appropriate
     */
    protected fun applyWindowInsetsAsMargins(view: android.view.View, applyTop: Boolean = true, applyBottom: Boolean = true) {
        EdgeToEdgeUtils.applyWindowInsetsAsMargins(view, applyTop, applyBottom)
    }

    /**
     * Check if this device requires edge-to-edge handling
     */
    protected fun requiresEdgeToEdgeHandling(): Boolean {
        return EdgeToEdgeUtils.requiresEdgeToEdgeHandling()
    }
}
