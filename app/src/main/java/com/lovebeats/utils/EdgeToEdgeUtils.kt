package com.lovebeats.utils

import android.app.Activity
import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import timber.log.Timber

/**
 * Utility class for handling edge-to-edge display on Android 15+
 * Provides backward compatibility and proper insets handling
 */
object EdgeToEdgeUtils {

    /**
     * Enable edge-to-edge display for the activity
     * This is the recommended approach for Android 15+ compatibility
     */
    fun enableEdgeToEdge(activity: Activity) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // For Android 11+ (API 30+), use the modern approach
                WindowCompat.setDecorFitsSystemWindows(activity.window, false)
                
                // Set transparent system bars
                activity.window.statusBarColor = android.graphics.Color.TRANSPARENT
                activity.window.navigationBarColor = android.graphics.Color.TRANSPARENT
                
                // Disable contrast enforcement for better edge-to-edge experience
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    activity.window.isStatusBarContrastEnforced = false
                    activity.window.isNavigationBarContrastEnforced = false
                }
                
                Timber.d("Edge-to-edge enabled for Android ${Build.VERSION.SDK_INT}")
            } else {
                // Fallback for older Android versions
                enableLegacyEdgeToEdge(activity)
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to enable edge-to-edge display")
            // Fallback to legacy approach
            enableLegacyEdgeToEdge(activity)
        }
    }

    /**
     * Legacy edge-to-edge implementation for older Android versions
     */
    private fun enableLegacyEdgeToEdge(activity: Activity) {
        try {
            activity.window.apply {
                // Enable drawing behind system bars
                addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                
                // Set transparent system bars
                statusBarColor = android.graphics.Color.TRANSPARENT
                navigationBarColor = android.graphics.Color.TRANSPARENT
                
                // Ensure light status bar for better visibility
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    decorView.systemUiVisibility = decorView.systemUiVisibility or 
                        View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                }
            }
            Timber.d("Legacy edge-to-edge enabled for Android ${Build.VERSION.SDK_INT}")
        } catch (e: Exception) {
            Timber.e(e, "Failed to enable legacy edge-to-edge display")
        }
    }

    /**
     * Apply window insets to a view to handle edge-to-edge content properly
     * This should be called on the root view or views that need inset handling
     */
    fun applyWindowInsets(view: View, applyTop: Boolean = true, applyBottom: Boolean = true) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            v.updatePadding(
                top = if (applyTop) systemBars.top else v.paddingTop,
                bottom = if (applyBottom) systemBars.bottom else v.paddingBottom
            )
            
            insets
        }
    }

    /**
     * Apply window insets with margins instead of padding
     * Useful for views that need margin-based inset handling
     */
    fun applyWindowInsetsAsMargins(view: View, applyTop: Boolean = true, applyBottom: Boolean = true) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            v.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                if (applyTop) topMargin = systemBars.top
                if (applyBottom) bottomMargin = systemBars.bottom
            }
            
            insets
        }
    }

    /**
     * Handle insets for bottom navigation views specifically
     * This ensures bottom navigation doesn't get hidden behind navigation bar
     */
    fun applyBottomNavigationInsets(bottomNavigationView: View) {
        ViewCompat.setOnApplyWindowInsetsListener(bottomNavigationView) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            v.updatePadding(bottom = systemBars.bottom)
            
            insets
        }
    }

    /**
     * Check if the device is running Android 15+ and requires edge-to-edge handling
     */
    fun requiresEdgeToEdgeHandling(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM // Android 15 (API 35)
    }

    /**
     * Get the current system bar insets
     * Useful for manual inset calculations
     */
    fun getSystemBarInsets(view: View): WindowInsetsCompat? {
        return ViewCompat.getRootWindowInsets(view)
    }
}
