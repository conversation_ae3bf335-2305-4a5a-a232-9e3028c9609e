<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:focusableInTouchMode="true">

    <ScrollView
        android:id="@+id/view_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/nav_bar_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="@dimen/edit_profile1_activity_nav_bar_constraint_layout_height"
                android:layout_marginEnd="@dimen/edit_profile1_activity_nav_bar_constraint_layout_margin_end"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:layout_editor_absoluteX="0dp"
                tools:layout_editor_absoluteY="0dp">

                <TextView
                    android:id="@+id/title_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="center"
                    android:lineSpacingMultiplier="1.09"
                    android:text="@string/edit_profile1_activity_title_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/edit_profile1_activity_title_text_view_text_size"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/save_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/edit_profile1_activity_left_text_view_margin_start"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:padding="5dp"
                    android:text="@string/edit_profile1_activity_left_text_view_text"
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/edit_profile1_activity_left_text_view_text_size"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/cancel_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/edit_profile1_activity_right_text_view_margin_end"
                    android:fontFamily="@font/inter"
                    android:gravity="end"
                    android:lineSpacingMultiplier="1.1"
                    android:padding="5dp"
                    android:text="@string/edit_profile1_activity_right_text_view_text"
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/edit_profile1_activity_right_text_view_text_size"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/field_profession_constraint_layout"
                android:layout_width="@dimen/edit_profile1_activity_field_profession_constraint_layout_width"
                android:layout_height="@dimen/edit_profile1_activity_field_profession_constraint_layout_height"
                android:layout_marginStart="@dimen/edit_profile1_activity_field_profession_constraint_layout_margin_start"
                android:layout_marginTop="24dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/the_basics_text_view">

                <EditText
                    android:id="@+id/profession_edit_text"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/edit_profile1_activity_value_edit_text_height"
                    android:layout_marginTop="@dimen/edit_profile1_activity_value_edit_text_margin_top"
                    android:background="@color/white"
                    android:fontFamily="@font/inter"
                    android:gravity="start|center_vertical"
                    android:hint="@string/edit_profile1_activity_value_edit_text_hint"
                    android:imeOptions="actionDone"
                    android:importantForAutofill="no"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textColor="@color/grey2"
                    android:textColorHint="@color/grey4"
                    android:textSize="@dimen/edit_profile1_activity_value_edit_text_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="20dp" />

                <TextView
                    android:id="@+id/field_title_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/edit_profile1_activity_field_title_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/edit_profile1_activity_field_title_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/edit_profile1_activity_field_title_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="-1dp" />

                <View
                    android:id="@+id/rectangle_constraint_layout"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/edit_profile1_activity_rectangle_constraint_layout_height"
                    android:background="@color/grey4"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="46dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/photos_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/edit_profile1_activity_photos_text_view_margin_start"
                android:layout_marginTop="@dimen/edit_profile1_activity_photos_text_view_margin_top"
                android:fontFamily="@font/inter_semibold"
                android:gravity="start"
                android:text="@string/edit_profile1_activity_photos_text_view_text"
                android:textColor="@color/grey1"
                android:textSize="@dimen/edit_profile1_activity_photos_text_view_text_size"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/nav_bar_constraint_layout"
                tools:layout_editor_absoluteX="16dp"
                tools:layout_editor_absoluteY="85dp" />

            <TextView
                android:id="@+id/profile_song_textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/edit_profile1_activity_the_basics_text_view_margin_start"
                android:layout_marginTop="18dp"
                android:fontFamily="@font/inter_semibold"
                android:gravity="start"
                android:text="Profile Song"
                android:textColor="@color/grey1"
                android:textSize="@dimen/edit_profile1_activity_the_basics_text_view_text_size"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/image_empty_copy4_constraint_layout" />

            <ImageView
                android:id="@+id/song_edit_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="24dp"
                android:padding="4dp"
                android:src="@drawable/edit_icon"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/profile_song_textView" />

            <com.lovebeats.customViews.PlayerView
                android:id="@+id/player"
                android:layout_width="0dp"
                android:layout_height="54dp"
                android:layout_marginTop="8dp"
                android:layout_marginStart="17dp"
                android:layout_marginEnd="24dp"
                android:background="@color/color_primary"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/profile_song_textView"/>

            <TextView
                android:id="@+id/favorites_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/edit_profile1_activity_the_basics_text_view_margin_start"
                android:layout_marginTop="18dp"
                android:fontFamily="@font/inter_semibold"
                android:gravity="start"
                android:text="Favorites"
                android:textColor="@color/grey1"
                android:textSize="@dimen/edit_profile1_activity_the_basics_text_view_text_size"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/player" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/favorites_view_recycler_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/favorites_text_view"
                tools:listitem="@layout/item_favorite_profile" />

            <ImageView
                android:id="@+id/favorites_edit_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="24dp"
                android:padding="4dp"
                android:src="@drawable/edit_icon"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/favorites_text_view" />

            <TextView
                android:id="@+id/genres_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/edit_profile1_activity_the_basics_text_view_margin_start"
                android:layout_marginTop="24dp"
                android:fontFamily="@font/inter_bold"
                android:gravity="start"
                android:text="Genres"
                android:textColor="@color/grey1"
                android:textSize="24sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/favorites_view_recycler_view" />

            <ImageView
                android:id="@+id/genres_edit_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="24dp"
                android:padding="4dp"
                android:src="@drawable/edit_icon"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/genres_text_view" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/genres_view_recycler_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="8dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/genres_text_view"
                tools:listitem="@layout/item_genres" />

            <TextView
                android:id="@+id/the_basics_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/edit_profile1_activity_the_basics_text_view_margin_start"
                android:layout_marginTop="18dp"
                android:fontFamily="@font/inter_semibold"
                android:gravity="start"
                android:text="@string/edit_profile1_activity_the_basics_text_view_text"
                android:textColor="@color/grey1"
                android:textSize="@dimen/edit_profile1_activity_the_basics_text_view_text_size"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/genres_view_recycler_view"
                tools:layout_editor_absoluteX="16dp"
                tools:layout_editor_absoluteY="366dp" />

            <TextView
                android:id="@+id/questions_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/edit_profile1_activity_questions_text_view_margin_start"
                android:layout_marginTop="18dp"
                android:fontFamily="@font/inter_semibold"
                android:gravity="start"
                android:text="@string/edit_profile1_activity_questions_text_view_text"
                android:textColor="@color/grey1"
                android:textSize="@dimen/edit_profile1_activity_questions_text_view_text_size"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/field_hometown_constraint_layout" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/field_education_constraint_layout"
                android:layout_width="@dimen/edit_profile1_activity_field_education_constraint_layout_width"
                android:layout_height="@dimen/edit_profile1_activity_field_education_constraint_layout_height"
                android:layout_marginStart="@dimen/edit_profile1_activity_field_education_constraint_layout_margin_start"
                android:layout_marginTop="24dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/field_profession_constraint_layout">

                <EditText
                    android:id="@+id/education_edit_text"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/edit_profile1_activity_value_two_edit_text_height"
                    android:layout_marginTop="@dimen/edit_profile1_activity_value_two_edit_text_margin_top"
                    android:background="@color/white"
                    android:fontFamily="@font/inter"
                    android:gravity="start|center_vertical"
                    android:hint="@string/edit_profile1_activity_value_two_edit_text_hint"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textColor="@color/grey1"
                    android:textColorHint="@color/grey4"
                    android:textSize="@dimen/edit_profile1_activity_value_two_edit_text_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="20dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/edit_profile1_activity_field_title_two_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/edit_profile1_activity_field_title_two_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/edit_profile1_activity_field_title_two_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="-1dp" />

                <View
                    android:id="@+id/rectangle_two_constraint_layout"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/edit_profile1_activity_rectangle_two_constraint_layout_height"
                    android:background="@color/grey4"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="46dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/add_another_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/edit_profile1_activity_add_another_text_view_margin_start"
                android:layout_marginTop="18dp"
                android:fontFamily="@font/inter_bold"
                android:gravity="start"
                android:lineSpacingMultiplier="1.09"
                android:padding="2dp"
                android:text="@string/edit_profile1_activity_add_another_text_view_text"
                android:textColor="@color/grey1"
                android:textSize="@dimen/edit_profile1_activity_add_another_text_view_text_size"
                android:visibility="visible"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/field_education_constraint_layout" />

            <EditText
                android:id="@+id/school2_edit_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="17dp"
                android:layout_marginTop="@dimen/edit_profile1_activity_add_another_text_view_margin_top"
                android:background="@color/white"
                android:fontFamily="@font/inter"
                android:gravity="start|center_vertical"
                android:hint="@string/edit_profile_education_2_hint"
                android:imeOptions="actionDone"
                android:inputType="text"
                android:maxLines="1"
                android:textColor="@color/grey1"
                android:textColorHint="@color/grey4"
                android:textSize="@dimen/edit_profile1_activity_value_two_edit_text_text_size"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/field_education_constraint_layout" />

            <View
                android:id="@+id/rectangle_two_constraint_layout_school2"
                android:layout_width="0dp"
                android:layout_height="@dimen/edit_profile1_activity_rectangle_two_constraint_layout_height"
                android:layout_marginEnd="48dp"
                android:background="@color/grey4"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="@+id/school2_edit_text"
                app:layout_constraintStart_toStartOf="@+id/school2_edit_text"
                app:layout_constraintTop_toBottomOf="@+id/school2_edit_text" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/field_hometown_constraint_layout"
                android:layout_width="@dimen/edit_profile1_activity_field_hometown_constraint_layout_width"
                android:layout_height="@dimen/edit_profile1_activity_field_hometown_constraint_layout_height"
                android:layout_marginStart="@dimen/edit_profile1_activity_field_hometown_constraint_layout_margin_start"
                android:layout_marginTop="24dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/add_another_text_view">

                <EditText
                    android:id="@+id/hometown_edit_text"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/edit_profile1_activity_value_three_edit_text_height"
                    android:layout_marginTop="@dimen/edit_profile1_activity_value_three_edit_text_margin_top"
                    android:layout_marginEnd="@dimen/edit_profile1_activity_value_three_edit_text_margin_end"
                    android:background="@color/white"
                    android:fontFamily="@font/inter"
                    android:gravity="start|center_vertical"
                    android:hint="@string/edit_profile1_activity_value_three_edit_text_hint"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textColor="@color/grey1"
                    android:textColorHint="@color/grey4"
                    android:textSize="@dimen/edit_profile1_activity_value_three_edit_text_text_size"
                    app:layout_constraintLeft_toRightOf="@+id/rectangle_three_constraint_layout"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="Autofill"
                    tools:layout_editor_absoluteX="2dp"
                    tools:layout_editor_absoluteY="20dp" />

                <View
                    android:id="@+id/rectangle_three_constraint_layout"
                    android:layout_width="@dimen/edit_profile1_activity_rectangle_three_constraint_layout_width"
                    android:layout_height="@dimen/edit_profile1_activity_rectangle_three_constraint_layout_height"
                    android:layout_marginTop="@dimen/edit_profile1_activity_rectangle_three_constraint_layout_margin_top"
                    android:background="@color/grey4"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/field_title_three_text_view"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="23dp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="@dimen/edit_profile1_activity_rectangle_four_constraint_layout_height"
                    android:background="@color/grey4"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="46dp" />

                <TextView
                    android:id="@+id/field_title_three_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/edit_profile1_activity_field_title_three_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/edit_profile1_activity_field_title_three_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/edit_profile1_activity_field_title_three_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="-1dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/row_question_copy2_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="@dimen/edit_profile1_activity_row_question_copy2_constraint_layout_height"
                android:layout_marginTop="@dimen/edit_profile1_activity_row_question_copy2_constraint_layout_margin_top"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/questions_text_view"
                tools:layout_editor_absoluteX="0dp"
                tools:layout_editor_absoluteY="727dp">

                <ImageView
                    android:id="@+id/icon_question_image_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/edit_profile1_activity_icon_question_image_view_margin_start"
                    android:scaleType="center"
                    android:src="@drawable/ic_icon_question_mark"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="ContentDescription"
                    tools:layout_editor_absoluteX="11dp"
                    tools:layout_editor_absoluteY="21dp" />

                <TextView
                    android:id="@+id/ice_breaker_1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/edit_profile1_activity_your_favorite_hobby_text_view_margin_start"
                    android:layout_marginEnd="16dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/edit_profile1_activity_your_favorite_hobby_text_view_text"
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/edit_profile1_activity_your_favorite_hobby_text_view_text_size"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/icon_question_image_view"
                    app:layout_constraintEnd_toStartOf="@+id/stre_right_image_view"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/stre_right_image_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/edit_profile1_activity_stre_right_image_view_margin_end"
                    android:scaleType="center"
                    android:src="@drawable/ic_arrow_right"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="318dp"
                    tools:layout_editor_absoluteY="24dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/row_question_copy3_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="@dimen/edit_profile1_activity_row_question_copy3_constraint_layout_height"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/row_question_copy2_constraint_layout"
                tools:layout_editor_absoluteX="0dp"
                tools:layout_editor_absoluteY="787dp">

                <ImageView
                    android:id="@+id/icon_question_two_image_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/edit_profile1_activity_icon_question_two_image_view_margin_start"
                    android:scaleType="center"
                    android:src="@drawable/ic_icon_question_mark"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="11dp"
                    tools:layout_editor_absoluteY="21dp" />

                <TextView
                    android:id="@+id/ice_breaker_2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/edit_profile1_activity_your_favorite_hobby_two_text_view_margin_start"
                    android:layout_marginEnd="16dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/edit_profile1_activity_your_favorite_hobby_two_text_view_text"
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/edit_profile1_activity_your_favorite_hobby_two_text_view_text_size"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/endArrow"
                    app:layout_constraintStart_toEndOf="@+id/icon_question_two_image_view"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/endArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/edit_profile1_activity_stre_right_two_image_view_margin_end"
                    android:scaleType="center"
                    android:src="@drawable/ic_arrow_right"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="318dp"
                    tools:layout_editor_absoluteY="24dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/image_empty_constraint_layout"
                android:layout_width="@dimen/edit_profile1_activity_image_empty_constraint_layout_width"
                android:layout_height="@dimen/edit_profile1_activity_image_empty_constraint_layout_height"
                app:layout_constraintEnd_toStartOf="@+id/image_empty_copy2_constraint_layout"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/image_empty_copy_constraint_layout"
                app:layout_constraintTop_toTopOf="@+id/image_empty_copy_constraint_layout">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="@dimen/images3_activity_rectangle_three_constraint_layout_height"
                    android:layout_marginStart="2dp"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="7dp"
                    android:background="@drawable/images3_activity_rectangle_three_constraint_layout_background"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/icon_camera_image_view1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
                    android:scaleType="center"
                    android:src="@drawable/icon_camera"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/icon_plus_addphoto_image_view1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scaleType="center"
                    android:src="@drawable/ic_add"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="73dp"
                    tools:layout_editor_absoluteY="0dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/image_empty_copy3_constraint_layout"
                android:layout_width="@dimen/edit_profile1_activity_image_empty_copy3_constraint_layout_width"
                android:layout_height="@dimen/edit_profile1_activity_image_empty_copy3_constraint_layout_height"
                android:layout_marginTop="32dp"
                app:layout_constraintEnd_toStartOf="@+id/image_empty_copy4_constraint_layout"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/image_empty_copy_constraint_layout">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="@dimen/images3_activity_rectangle_three_constraint_layout_height"
                    android:layout_marginStart="2dp"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="7dp"
                    android:background="@drawable/images3_activity_rectangle_three_constraint_layout_background"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/icon_plus_addphoto_image_view3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scaleType="center"
                    android:src="@drawable/ic_add"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="73dp"
                    tools:layout_editor_absoluteY="0dp" />

                <ImageView
                    android:id="@+id/icon_camera_image_view3"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
                    android:scaleType="center"
                    android:src="@drawable/icon_camera"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/image_empty_copy4_constraint_layout"
                android:layout_width="@dimen/edit_profile1_activity_image_empty_copy4_constraint_layout_width"
                android:layout_height="@dimen/edit_profile1_activity_image_empty_copy4_constraint_layout_height"
                android:layout_marginTop="32dp"
                app:layout_constraintEnd_toStartOf="@+id/image_empty_copy5_constraint_layout"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/image_empty_copy3_constraint_layout"
                app:layout_constraintTop_toBottomOf="@+id/image_empty_constraint_layout">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="@dimen/images3_activity_rectangle_three_constraint_layout_height"
                    android:layout_marginStart="2dp"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="7dp"
                    android:background="@drawable/images3_activity_rectangle_three_constraint_layout_background"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/icon_plus_addphoto_image_view4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scaleType="center"
                    android:src="@drawable/ic_add"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="73dp"
                    tools:layout_editor_absoluteY="0dp" />

                <ImageView
                    android:id="@+id/icon_camera_image_view4"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
                    android:scaleType="center"
                    android:src="@drawable/icon_camera"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/image_empty_copy5_constraint_layout"
                android:layout_width="@dimen/edit_profile1_activity_image_empty_copy5_constraint_layout_width"
                android:layout_height="@dimen/edit_profile1_activity_image_empty_copy5_constraint_layout_height"
                android:layout_marginTop="32dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/image_empty_copy4_constraint_layout"
                app:layout_constraintTop_toBottomOf="@+id/image_empty_copy2_constraint_layout">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="@dimen/images3_activity_rectangle_three_constraint_layout_height"
                    android:layout_marginStart="2dp"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="7dp"
                    android:background="@drawable/images3_activity_rectangle_three_constraint_layout_background"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/icon_plus_addphoto_image_view5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scaleType="center"
                    android:src="@drawable/ic_add"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="73dp"
                    tools:layout_editor_absoluteY="0dp" />

                <ImageView
                    android:id="@+id/icon_camera_image_view5"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
                    android:scaleType="center"
                    android:src="@drawable/icon_camera"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/image_empty_copy2_constraint_layout"
                android:layout_width="@dimen/edit_profile1_activity_image_empty_copy2_constraint_layout_width"
                android:layout_height="@dimen/edit_profile1_activity_image_empty_copy2_constraint_layout_height"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/image_empty_constraint_layout"
                app:layout_constraintTop_toTopOf="@+id/image_empty_copy_constraint_layout">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="@dimen/images3_activity_rectangle_three_constraint_layout_height"
                    android:layout_marginStart="2dp"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="7dp"
                    android:background="@drawable/images3_activity_rectangle_three_constraint_layout_background"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/icon_camera_image_view2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
                    android:scaleType="center"
                    android:src="@drawable/icon_camera"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/icon_plus_addphoto_image_view2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scaleType="center"
                    android:src="@drawable/ic_add"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="73dp"
                    tools:layout_editor_absoluteY="0dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/image_empty_copy_constraint_layout"
                android:layout_width="@dimen/edit_profile1_activity_image_empty_copy_constraint_layout_width"
                android:layout_height="@dimen/edit_profile1_activity_image_empty_copy_constraint_layout_height"
                android:layout_marginTop="12dp"
                app:layout_constraintEnd_toStartOf="@+id/image_empty_constraint_layout"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/photos_text_view">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="@dimen/images3_activity_rectangle_three_constraint_layout_height"
                    android:layout_marginStart="2dp"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="7dp"
                    android:background="@drawable/images3_activity_rectangle_three_constraint_layout_background"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/icon_camera_image_view0"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
                    android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
                    android:scaleType="center"
                    android:src="@drawable/icon_camera"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/icon_plus_addphoto_image_view0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scaleType="center"
                    android:src="@drawable/ic_add"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="73dp"
                    tools:layout_editor_absoluteY="0dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <ProgressBar
        android:id="@+id/edit_profile_progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>